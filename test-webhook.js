const { Client } = require('pg');
require('dotenv').config();

// Test script to simulate a game assignment webhook
async function testGameAssignmentWebhook() {
    const client = new Client({
        connectionString: process.env.DATABASE_URL,
    });

    try {
        await client.connect();
        console.log('Connected to PostgreSQL');

        // Test data - replace these with actual IDs from your database
        const testData = {
            party_id: 'your-party-id-here',  // Replace with actual party ID
            game_id: 'your-game-id-here',    // Replace with actual game ID
            assigned_at: new Date().toISOString()
        };

        console.log('Testing with data:', testData);

        // Call the database function directly
        const result = await client.query(
            'SELECT recall_loop.send_game_assignment_email($1, $2, $3) as result',
            [testData.party_id, testData.game_id, testData.assigned_at]
        );

        console.log('Function result:', result.rows[0].result);

        // Alternative: Send a direct notification (if you want to test the webhook listener)
        const notificationPayload = {
            party_id: testData.party_id,
            game_id: testData.game_id,
            assigned_at: testData.assigned_at,
            user_email: '<EMAIL>',
            user_name: 'Test User',
            game_name: 'Test Game',
            party_type: 'caregiver'
        };

        await client.query(
            "SELECT pg_notify('game_assignment_email', $1)",
            [JSON.stringify(notificationPayload)]
        );

        console.log('Notification sent to webhook listener');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.end();
    }
}

// Test the edge function directly
async function testEdgeFunction() {
    const testPayload = {
        party_id: 'your-party-id-here',  // Replace with actual party ID
        game_id: 'your-game-id-here',    // Replace with actual game ID
        assigned_at: new Date().toISOString()
    };

    try {
        const response = await fetch('http://localhost:54321/functions/v1/GameAssignmentEmail', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify(testPayload)
        });

        const result = await response.json();
        console.log('Edge function response:', result);
    } catch (error) {
        console.error('Error testing edge function:', error);
    }
}

// Run tests
console.log('Choose test method:');
console.log('1. Test database function (node test-webhook.js db)');
console.log('2. Test edge function (node test-webhook.js edge)');

const testType = process.argv[2];

if (testType === 'db') {
    testGameAssignmentWebhook();
} else if (testType === 'edge') {
    testEdgeFunction();
} else {
    console.log('Please specify test type: "db" or "edge"');
    console.log('Example: node test-webhook.js db');
}
