-- Create a function to call the edge function when a game assignment is inserted
CREATE OR R<PERSON>LACE FUNCTION recall_loop.trigger_game_assignment_email()
RETURNS TRIGGER AS $$
DECLARE
    webhook_url text;
    payload jsonb;
    response_status int;
BEGIN
    -- Get the webhook URL from environment or set it directly
    -- You'll need to replace this with your actual edge function URL
    webhook_url := 'https://your-project-ref.supabase.co/functions/v1/GameAssignmentEmail';
    
    -- Prepare the payload
    payload := jsonb_build_object(
        'party_id', NEW.party_id,
        'game_id', NEW.game_id,
        'assigned_at', NEW.assigned_at
    );
    
    -- Call the edge function using pg_net extension
    -- Note: You need to enable the pg_net extension first
    SELECT status INTO response_status
    FROM net.http_post(
        url := webhook_url,
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true)
        ),
        body := payload
    );
    
    -- Log the response (optional)
    RAISE NOTICE 'Webhook called with status: %', response_status;
    
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the insert
    RAISE WARNING 'Failed to call webhook: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS game_assignment_email_trigger ON recall_loop.game_assignment;
CREATE TRIGGER game_assignment_email_trigger
    AFTER INSERT ON recall_loop.game_assignment
    FOR EACH ROW
    EXECUTE FUNCTION recall_loop.trigger_game_assignment_email();

-- Enable the pg_net extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA net TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA net TO postgres, anon, authenticated, service_role;
