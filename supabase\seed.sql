-- Enhanced function to list all tenants with caretaker and player counts
CREATE OR REPLACE FUNCTION public.fn_list_all_tenants()
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
-- To list all tenant details with caretaker and player counts
DECLARE
  tenant_type_id uuid;
  caretaker_type_id uuid;
  player_type_id uuid;
  all_tenants jsonb;
BEGIN

  -- Get tenant party type ID
  SELECT id INTO tenant_type_id
  FROM recall_loop.party_type
  WHERE party_type_key = 'TENANT';

  -- Get caretaker party type ID
  SELECT id INTO caretaker_type_id
  FROM recall_loop.party_type
  WHERE party_type_key = 'CARETAKER';

  -- Get player party type ID
  SELECT id INTO player_type_id
  FROM recall_loop.party_type
  WHERE party_type_key = 'PLAYER';

  IF tenant_type_id IS NULL THEN
    RETURN jsonb_build_object(
      'status', 'error',
      'message', 'Tenant party type not found'
    );
  END IF;

  WITH tenant_data AS (
    SELECT
      p.id,
      p.name AS tenant_name,
      p.is_active,
      p.tenant_code,
      pt.party_type_key,
      (SELECT per.avatar_url FROM recall_loop.person per WHERE per.party_id = p.id LIMIT 1) AS avatar_url,
      (SELECT per.first_name FROM recall_loop.person per WHERE per.party_id = p.id LIMIT 1) AS first_name,
      (SELECT per.last_name FROM recall_loop.person per WHERE per.party_id = p.id LIMIT 1) AS last_name,
      (SELECT per.phone_number FROM recall_loop.person per WHERE per.party_id = p.id LIMIT 1) AS phone_number,
      (SELECT ua.email FROM recall_loop.user_account ua WHERE ua.party_id = p.id LIMIT 1) AS email,
      p.created_at,
      -- Count caretakers under this tenant from party_relationship table
      (SELECT COUNT(*)
       FROM recall_loop.party_relationship pr
       JOIN recall_loop.party caretaker_party ON pr.child_party_id = caretaker_party.id
       WHERE pr.parent_party_id = p.id  -- tenant is parent
         AND pr.is_active = TRUE
         AND pr.is_deleted = FALSE
         AND caretaker_party.party_type_id = caretaker_type_id  -- child is caretaker
         AND caretaker_party.is_deleted = FALSE
         AND caretaker_party.is_active = TRUE
      ) AS caretaker_count,
      -- Count players under this tenant from party_relationship table
      (SELECT COUNT(*)
       FROM recall_loop.party_relationship pr
       JOIN recall_loop.party player_party ON pr.child_party_id = player_party.id
       WHERE pr.parent_party_id = p.id  -- tenant is parent
         AND pr.is_active = TRUE
         AND pr.is_deleted = FALSE
         AND player_party.party_type_id = player_type_id  -- child is player
         AND player_party.is_deleted = FALSE
         AND player_party.is_active = TRUE
      ) AS player_count
    FROM recall_loop.party p
    JOIN recall_loop.party_type pt ON p.party_type_id = pt.id
    WHERE p.is_deleted = FALSE
      AND p.party_type_id = tenant_type_id
    ORDER BY p.created_at DESC
  )
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', td.id,
      'tenant_name', td.tenant_name,
      'email', td.email,
      'status', td.is_active,
      'avatar_url', td.avatar_url,
      'party_type', td.party_type_key,
      'tenant_code', td.tenant_code,
      'first_name', td.first_name,
      'last_name', td.last_name,
      'phone_number', td.phone_number,
      'created_at', td.created_at,
      'caretaker_count', td.caretaker_count,
      'player_count', td.player_count,
      'total_users', td.caretaker_count + td.player_count
    )
  ) INTO all_tenants
  FROM tenant_data td;

  RETURN jsonb_build_object(
    'status', 'success',
    'data', COALESCE(all_tenants, '[]'::jsonb)
  );

EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'status', 'error',
    'message', 'Failed to retrieve tenants',
    'error', SQLERRM
  );
END;
$function$;