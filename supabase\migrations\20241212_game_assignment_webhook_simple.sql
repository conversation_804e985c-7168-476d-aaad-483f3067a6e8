-- Alternative approach: Create a function that can be called directly from your application
-- This function will handle the email sending logic within the database

CREATE OR REPLACE FUNCTION recall_loop.send_game_assignment_email(
    p_party_id uuid,
    p_game_id uuid,
    p_assigned_at timestamp with time zone
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_party_type_id uuid;
    v_party_type_key text;
    v_user_email text;
    v_user_name text;
    v_game_name text;
    v_result jsonb;
BEGIN
    -- Step 1: Get party_type_id from game_assignment table
    SELECT party_type_id INTO v_party_type_id
    FROM recall_loop.game_assignment
    WHERE id = p_party_id;
    
    IF v_party_type_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Game assignment not found'
        );
    END IF;
    
    -- Step 2: Get party_type_key from party_type table
    SELECT party_type_key INTO v_party_type_key
    FROM recall_loop.party_type
    WHERE id = v_party_type_id;
    
    IF v_party_type_key IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Party type not found'
        );
    END IF;
    
    -- Step 3: Check if party_type_key is 'caregiver' or 'tenant_admin'
    IF LOWER(v_party_type_key) NOT IN ('caregiver', 'tenant_admin') THEN
        RETURN jsonb_build_object(
            'status', 'success',
            'message', 'Party type not eligible for email notification',
            'party_type', v_party_type_key
        );
    END IF;
    
    -- Step 4: Get user email from auth.users via user_account
    SELECT au.email INTO v_user_email
    FROM recall_loop.user_account ua
    JOIN auth.users au ON ua.user_id = au.id
    WHERE ua.party_id = p_party_id;
    
    IF v_user_email IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'User email not found'
        );
    END IF;
    
    -- Step 5: Get user name from person table
    SELECT TRIM(CONCAT(first_name, ' ', last_name)) INTO v_user_name
    FROM recall_loop.person
    WHERE party_id = p_party_id;
    
    IF v_user_name IS NULL OR v_user_name = '' THEN
        v_user_name := 'User';
    END IF;
    
    -- Step 6: Get game name
    SELECT name INTO v_game_name
    FROM recall_loop.game
    WHERE id = p_game_id;
    
    IF v_game_name IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Game not found'
        );
    END IF;
    
    -- Step 7: Send notification via PostgreSQL NOTIFY
    -- This will be picked up by your webhook listener
    PERFORM pg_notify('game_assignment_email', jsonb_build_object(
        'party_id', p_party_id,
        'game_id', p_game_id,
        'assigned_at', p_assigned_at,
        'user_email', v_user_email,
        'user_name', v_user_name,
        'game_name', v_game_name,
        'party_type', v_party_type_key
    )::text);
    
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Email notification queued',
        'details', jsonb_build_object(
            'user_email', v_user_email,
            'user_name', v_user_name,
            'game_name', v_game_name,
            'party_type', v_party_type_key
        )
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'status', 'error',
        'message', 'Failed to process email notification',
        'error', SQLERRM
    );
END;
$$;

-- Create a trigger function that calls the email function
CREATE OR REPLACE FUNCTION recall_loop.trigger_game_assignment_email_simple()
RETURNS TRIGGER AS $$
DECLARE
    result jsonb;
BEGIN
    -- Call the email function
    SELECT recall_loop.send_game_assignment_email(
        NEW.party_id,
        NEW.game_id,
        NEW.assigned_at
    ) INTO result;
    
    -- Log the result
    RAISE NOTICE 'Game assignment email result: %', result;
    
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the insert
    RAISE WARNING 'Failed to trigger email notification: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS game_assignment_email_trigger_simple ON recall_loop.game_assignment;
CREATE TRIGGER game_assignment_email_trigger_simple
    AFTER INSERT ON recall_loop.game_assignment
    FOR EACH ROW
    EXECUTE FUNCTION recall_loop.trigger_game_assignment_email_simple();

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION recall_loop.send_game_assignment_email(uuid, uuid, timestamp with time zone) TO authenticated, service_role;
