const express = require('express');
const { Client } = require('pg');
const nodemailer = require('nodemailer');
require('dotenv').config();

const app = express();
const port = 3001;

// PostgreSQL client
const client = new Client({
    connectionString: process.env.DATABASE_URL, // Your Supabase or PostgreSQL database URL
});

// Create transporter for sending emails
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: true, // Use true for 465, false for other ports
    auth: {
        user: process.env.SMTP_USER, // Your SMTP username
        pass: process.env.SMTP_PASS, // Your SMTP password
    },
});

// Middleware to parse JSON requests
app.use(express.json());

// Simple GET route for testing
app.get('/', (_req, res) => {
    res.send('Webhook server is running');
});

// Connect to PostgreSQL and listen for notifications
client.connect()
    .then(() => {
        console.log('Connected to PostgreSQL');
        return client.query('LISTEN send_mail'); 
    })
    .catch(err => console.error('Connection error', err.stack));

// Handle notifications from PostgreSQL
client.on('notification', async (msg) => {
    const data = JSON.parse(msg.payload);
    console.log('Received notification:', data); 

    const userId = data.user_id;
    const fcmToken = data.fcm_token;
    const recipientEmail = data.email; 
    const mailContent = data.mail_content;

    try {
        // Send email using Nodemailer
        await transporter.sendMail({
            from: process.env.SMTP_USER, 
            to: recipientEmail, 
            subject: 'Course Module Notification', 
            text: `Message: ${mailContent}\n\nUser ID: ${userId}\n\nFCM Token: ${fcmToken}`, 
        });
        console.log('Email sent to:', recipientEmail);
    } catch (error) {
        console.error('Error sending email:', error); 
    }
});

// Start the Express server
app.listen(port, () => {
    console.log(`Webhook server is running on http://localhost:${port}`);
});