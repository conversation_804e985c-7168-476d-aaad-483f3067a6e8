const express = require('express');
const { Client } = require('pg');
const nodemailer = require('nodemailer');
require('dotenv').config();

const app = express();
const port = 3001;

// PostgreSQL client
const client = new Client({
    connectionString: process.env.DATABASE_URL, // Your Supabase or PostgreSQL database URL
});

// Create transporter for sending emails
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: true, // Use true for 465, false for other ports
    auth: {
        user: process.env.SMTP_USER, // Your SMTP username
        pass: process.env.SMTP_PASS, // Your SMTP password
    },
});

// Middleware to parse JSON requests
app.use(express.json());

// Simple GET route for testing
app.get('/', (_req, res) => {
    res.send('Webhook server is running');
});

// Connect to PostgreSQL and listen for notifications
client.connect()
    .then(() => {
        console.log('Connected to PostgreSQL');
        // Listen for both existing and new game assignment notifications
        return Promise.all([
            client.query('LISTEN send_mail'),
            client.query('LISTEN game_assignment_email')
        ]);
    })
    .catch(err => console.error('Connection error', err.stack));

// Email template function
function generateGameAssignmentEmail(userName, gameName, assignedAt) {
    return `
<p>Dear ${userName},</p>

<p>Great news! You've been assigned a new game on the Competitor LDC Coaching Centre platform.</p>

<p>
  <strong>Game:</strong> ${gameName}<br>
  <strong>Assigned On:</strong> ${assignedAt}
</p>

<p>
  Please log in to your account to start playing and continue your progress.
</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
    `;
}

// Handle notifications from PostgreSQL
client.on('notification', async (msg) => {
    const data = JSON.parse(msg.payload);
    console.log('Received notification:', data);

    try {
        if (msg.channel === 'game_assignment_email') {
            // Handle game assignment email notification
            const { user_email, user_name, game_name, assigned_at } = data;

            if (!user_email || !user_name || !game_name) {
                console.error('Missing required data for game assignment email:', data);
                return;
            }

            const assignedAtFormatted = new Date(assigned_at).toLocaleDateString();
            const emailContent = generateGameAssignmentEmail(user_name, game_name, assignedAtFormatted);

            await transporter.sendMail({
                from: process.env.SMTP_USER,
                to: user_email,
                subject: 'New Game Assignment - Competitor LDC Coaching Centre',
                html: emailContent,
            });

            console.log(`Game assignment email sent to: ${user_email} for game: ${game_name}`);

        } else if (msg.channel === 'send_mail') {
            // Handle existing course module notification
            const userId = data.user_id;
            const fcmToken = data.fcm_token;
            const recipientEmail = data.email;
            const mailContent = data.mail_content;

            await transporter.sendMail({
                from: process.env.SMTP_USER,
                to: recipientEmail,
                subject: 'Course Module Notification',
                text: `Message: ${mailContent}\n\nUser ID: ${userId}\n\nFCM Token: ${fcmToken}`,
            });
            console.log('Course module email sent to:', recipientEmail);
        }
    } catch (error) {
        console.error('Error sending email:', error);
    }
});

// Start the Express server
app.listen(port, () => {
    console.log(`Webhook server is running on http://localhost:${port}`);
});